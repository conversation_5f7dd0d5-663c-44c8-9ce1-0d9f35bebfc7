"""
联邦学习数据加载器
负责处理数据分割、客户端数据分配和文本数据生成
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
import json
import pickle
import os
from typing import Dict, List, Tuple, Optional
import random

class FederatedDataset(Dataset):
    """联邦学习数据集"""
    
    def __init__(self, user_sequences: List[List[int]], 
                 pos_items: List[int], neg_items: List[int],
                 text_data: Optional[List[str]] = None):
        self.user_sequences = user_sequences
        self.pos_items = pos_items
        self.neg_items = neg_items
        self.text_data = text_data or []
        
    def __len__(self):
        return len(self.user_sequences)
    
    def __getitem__(self, idx):
        user_seq = torch.tensor(self.user_sequences[idx], dtype=torch.long)
        pos_item = torch.tensor(self.pos_items[idx], dtype=torch.long)
        neg_item = torch.tensor(self.neg_items[idx], dtype=torch.long)
        
        if self.text_data:
            return user_seq, pos_item, neg_item, self.text_data[idx]
        else:
            return user_seq, pos_item, neg_item

class FederatedDataLoader:
    """联邦学习数据加载器"""
    
    def __init__(self, args):
        self.args = args
        self.data_dir = args.data_dir
        self.dataset = args.dataset
        self.client_num = args.client_num
        self.maxlen = args.maxlen
        
        # 加载原始数据
        self.raw_data = self._load_raw_data()
        
        # 分割数据到客户端
        self.client_data = self._split_data_to_clients()
        
        # 生成文本数据
        self.text_data = self._generate_text_data()
        
    def _load_raw_data(self) -> Dict:
        """加载原始数据"""
        data_path = os.path.join(self.data_dir, self.dataset)
        
        # 加载用户-物品交互数据
        train_file = os.path.join(data_path, 'train.txt')
        valid_file = os.path.join(data_path, 'valid.txt')
        test_file = os.path.join(data_path, 'test.txt')
        
        train_data = self._load_interaction_file(train_file)
        valid_data = self._load_interaction_file(valid_file)
        test_data = self._load_interaction_file(test_file)
        
        # 加载物品元数据
        item_meta_file = os.path.join(data_path, 'item_meta.json')
        item_meta = {}
        if os.path.exists(item_meta_file):
            with open(item_meta_file, 'r') as f:
                item_meta = json.load(f)
        
        return {
            'train': train_data,
            'valid': valid_data,
            'test': test_data,
            'item_meta': item_meta
        }
    
    def _load_interaction_file(self, file_path: str) -> Dict:
        """加载交互文件"""
        user_sequences = {}
        
        if not os.path.exists(file_path):
            return user_sequences
        
        with open(file_path, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    user_id = int(parts[0])
                    items = [int(x) for x in parts[1:]]
                    user_sequences[user_id] = items
        
        return user_sequences
    
    def _split_data_to_clients(self) -> Dict:
        """将数据分割到不同客户端"""
        client_data = {
            'train': {},
            'valid': {},
            'test': {}
        }
        
        # 获取所有用户ID
        all_users = set()
        for split in ['train', 'valid', 'test']:
            all_users.update(self.raw_data[split].keys())
        
        all_users = list(all_users)
        random.shuffle(all_users)
        
        # 将用户分配到客户端
        users_per_client = len(all_users) // self.client_num
        
        for client_id in range(self.client_num):
            start_idx = client_id * users_per_client
            if client_id == self.client_num - 1:
                end_idx = len(all_users)
            else:
                end_idx = (client_id + 1) * users_per_client
            
            client_users = all_users[start_idx:end_idx]
            
            # 为每个客户端创建数据
            for split in ['train', 'valid', 'test']:
                client_data[split][client_id] = self._create_client_dataset(
                    client_users, split
                )
        
        return client_data
    
    def _create_client_dataset(self, client_users: List[int], split: str) -> Dict:
        """为客户端创建数据集"""
        user_sequences = []
        pos_items = []
        neg_items = []
        text_inputs = []
        
        split_data = self.raw_data[split]
        item_meta = self.raw_data['item_meta']
        
        for user_id in client_users:
            if user_id not in split_data:
                continue
            
            user_items = split_data[user_id]
            if len(user_items) < 2:
                continue
            
            # 创建序列数据
            for i in range(1, len(user_items)):
                # 输入序列（截断到maxlen-1）
                input_seq = user_items[:i]
                if len(input_seq) > self.maxlen - 1:
                    input_seq = input_seq[-(self.maxlen-1):]
                
                # 填充序列
                padded_seq = [0] * (self.maxlen - len(input_seq)) + input_seq
                
                # 正样本
                pos_item = user_items[i]
                
                # 负样本（随机采样）
                neg_item = self._sample_negative_item(user_items)
                
                user_sequences.append(padded_seq)
                pos_items.append(pos_item)
                neg_items.append(neg_item)
                
                # 生成文本输入
                text_input = self._generate_text_input(
                    user_id, input_seq, pos_item, item_meta
                )
                text_inputs.append(text_input)
        
        # 创建数据加载器
        dataset = FederatedDataset(user_sequences, pos_items, neg_items, text_inputs)
        dataloader = DataLoader(
            dataset, 
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=2
        )
        
        return {
            'dataset': dataset,
            'dataloader': dataloader,
            'num_users': len(client_users),
            'num_interactions': len(user_sequences)
        }
    
    def _sample_negative_item(self, user_items: List[int]) -> int:
        """采样负样本"""
        # 简单的随机负采样
        max_item_id = self.args.item_num
        while True:
            neg_item = random.randint(1, max_item_id)
            if neg_item not in user_items:
                return neg_item
    
    def _generate_text_input(self, user_id: int, user_seq: List[int], 
                           target_item: int, item_meta: Dict) -> str:
        """生成文本输入"""
        # 获取物品名称
        def get_item_name(item_id):
            if str(item_id) in item_meta:
                return item_meta[str(item_id)].get('title', f'Item_{item_id}')
            return f'Item_{item_id}'
        
        # 构建历史序列文本
        history_names = [get_item_name(item_id) for item_id in user_seq[-5:]]
        history_text = ', '.join(history_names)
        
        # 构建推荐文本
        target_name = get_item_name(target_item)
        
        text_input = (
            f"User {user_id} has interacted with the following items: {history_text}. "
            f"Based on this history, recommend whether the user would like {target_name}."
        )
        
        return text_input
    
    def _generate_text_data(self) -> Dict:
        """生成文本数据用于知识蒸馏"""
        text_data = {}
        
        for split in ['train', 'valid', 'test']:
            text_data[split] = {}
            for client_id in range(self.client_num):
                if client_id in self.client_data[split]:
                    dataset = self.client_data[split][client_id]['dataset']
                    text_data[split][client_id] = dataset.text_data
        
        return text_data
    
    def get_client_data(self, client_id: int, split: str) -> Dict:
        """获取指定客户端的数据"""
        if client_id in self.client_data[split]:
            return self.client_data[split][client_id]
        else:
            return {
                'dataset': None,
                'dataloader': None,
                'num_users': 0,
                'num_interactions': 0
            }
    
    def get_text_inputs(self, round_idx: int) -> List[str]:
        """获取当前轮次的文本输入"""
        # 从训练数据中随机采样文本输入用于知识蒸馏
        all_text_inputs = []
        
        for client_id in range(self.client_num):
            if client_id in self.text_data['train']:
                client_texts = self.text_data['train'][client_id]
                # 随机采样一部分文本
                sample_size = min(len(client_texts), 100)
                sampled_texts = random.sample(client_texts, sample_size)
                all_text_inputs.extend(sampled_texts)
        
        return all_text_inputs
    
    def get_statistics(self) -> Dict:
        """获取数据统计信息"""
        stats = {
            'total_users': 0,
            'total_interactions': 0,
            'clients': {}
        }
        
        for split in ['train', 'valid', 'test']:
            for client_id in range(self.client_num):
                if client_id not in stats['clients']:
                    stats['clients'][client_id] = {
                        'users': 0,
                        'interactions': {'train': 0, 'valid': 0, 'test': 0}
                    }
                
                client_data = self.get_client_data(client_id, split)
                stats['clients'][client_id]['users'] = client_data['num_users']
                stats['clients'][client_id]['interactions'][split] = client_data['num_interactions']
                
                if split == 'train':
                    stats['total_users'] += client_data['num_users']
                    stats['total_interactions'] += client_data['num_interactions']
        
        return stats
