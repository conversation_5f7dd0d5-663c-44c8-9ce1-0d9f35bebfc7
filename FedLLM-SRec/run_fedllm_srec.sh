#!/bin/bash

# FedLLM-SRec 训练启动脚本
# 联邦学习大小模型协同推荐系统

echo "=========================================="
echo "FedLLM-SRec: 联邦学习大小模型协同推荐系统"
echo "=========================================="

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export TOKENIZERS_PARALLELISM=false
export OMP_NUM_THREADS=4

# 创建必要的目录
mkdir -p logs
mkdir -p models
mkdir -p checkpoints
mkdir -p outputs
mkdir -p data

echo "创建目录完成..."

# 检查GPU可用性
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}')"

# 基础配置
DATASET="Movies_and_TV"
CLIENT_NUM=5
FED_ROUNDS=50
BATCH_SIZE=32
LEARNING_RATE=1e-4

# 模型配置
BASE_MODEL="meta-llama/Llama-2-7b-hf"
LORA_R=8
LORA_ALPHA=16

echo "开始训练 FedLLM-SRec 模型..."
echo "数据集: $DATASET"
echo "客户端数量: $CLIENT_NUM"
echo "联邦轮次: $FED_ROUNDS"
echo "基础模型: $BASE_MODEL"

# 主训练命令
python train_federated_llm_srec.py \
    --device cuda:0 \
    --seed 42 \
    --dataset $DATASET \
    --data_dir ./data \
    --log_dir ./logs \
    --model_dir ./models \
    --checkpoint_dir ./checkpoints \
    \
    --client_num $CLIENT_NUM \
    --fed_rounds $FED_ROUNDS \
    --similarity_threshold 0.5 \
    --alpha 0.7 \
    --beta 1 \
    \
    --base_model $BASE_MODEL \
    --hidden_units 256 \
    --maxlen 128 \
    --dropout_rate 0.2 \
    \
    --lora_r $LORA_R \
    --lora_alpha $LORA_ALPHA \
    --lora_dropout 0.05 \
    \
    --pretrain_clients \
    --pretrain_epochs 10 \
    --client_lr 1e-3 \
    --server_lr $LEARNING_RATE \
    --weight_decay 1e-5 \
    --uniformity_weight 0.1 \
    \
    --eval_interval 5 \
    --patience 10 \
    --save_interval 10 \
    2>&1 | tee logs/training_$(date +%Y%m%d_%H%M%S).log

echo "训练完成！"

# 运行推理评估
echo "开始最终评估..."
python inference_fedllm_srec.py \
    --model_dir ./models \
    --data_dir ./data \
    --dataset $DATASET \
    --client_num $CLIENT_NUM \
    --batch_size 128 \
    --top_k 10 20 50 \
    2>&1 | tee logs/inference_$(date +%Y%m%d_%H%M%S).log

echo "评估完成！"

# 生成实验报告
echo "生成实验报告..."
python utils/generate_report.py \
    --log_dir ./logs \
    --output_dir ./outputs \
    --experiment_name "fedllm_srec_${DATASET}_$(date +%Y%m%d)"

echo "实验报告已生成到 ./outputs/ 目录"

echo "=========================================="
echo "FedLLM-SRec 实验完成！"
echo "=========================================="
