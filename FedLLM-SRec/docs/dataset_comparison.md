# FedLLM-SRec 数据集对比和选择指南

## 📊 数据集概览

### 原项目使用的数据集

| 项目 | 数据集 | 版本 | 规模 | 特点 |
|------|--------|------|------|------|
| **FELLRec** | Video_Games | Amazon 2018 | ~500K交互 | 游戏推荐，丰富的元数据 |
| **LLM-SRec** | Movies_and_TV | Amazon 2023 | ~1M交互 | 电影电视推荐，文本丰富 |
| **LLM-SRec** | Industrial_and_Scientific | Amazon 2023 | ~200K交互 | 工业科学产品，专业性强 |

### 推荐的数据集选择

## 🎯 主要推荐数据集

### 1. Movies_and_TV (首选)
**优势：**
- ✅ LLM-SRec项目已验证
- ✅ 数据量适中（~1M交互）
- ✅ 文本信息丰富，适合大模型
- ✅ 用户行为模式清晰
- ✅ 联邦学习场景真实（不同用户偏好差异大）

**数据特点：**
```
用户数量: ~50,000
物品数量: ~30,000
平均序列长度: 15-20
文本质量: 高（标题、描述、评论）
领域特点: 娱乐内容，用户偏好多样化
```

**使用建议：**
- 适合作为主要实验数据集
- 可以验证大小模型协同效果
- 联邦学习效果明显

### 2. Industrial_and_Scientific (次选)
**优势：**
- ✅ LLM-SRec项目已验证
- ✅ 数据质量高
- ✅ 专业领域，用户行为稳定
- ✅ 适合测试模型泛化能力

**数据特点：**
```
用户数量: ~20,000
物品数量: ~15,000
平均序列长度: 8-12
文本质量: 中等（技术描述较多）
领域特点: 专业产品，用户需求明确
```

**使用建议：**
- 适合作为对比实验数据集
- 验证模型在专业领域的效果
- 测试知识蒸馏的有效性

### 3. Video_Games (可选)
**优势：**
- ✅ FELLRec项目已验证
- ✅ 游戏推荐场景真实
- ✅ 用户粘性高，序列模式明显
- ✅ 元数据丰富

**数据特点：**
```
用户数量: ~40,000
物品数量: ~25,000
平均序列长度: 12-18
文本质量: 高（游戏描述详细）
领域特点: 游戏娱乐，用户忠诚度高
```

**使用建议：**
- 适合验证联邦学习效果
- 可以与FELLRec结果对比
- 测试序列建模能力

## 📋 数据集准备步骤

### 快速开始（推荐）

```bash
# 1. 下载和准备Movies_and_TV数据集
cd FedLLM-SRec
chmod +x scripts/download_and_prepare_data.sh
./scripts/download_and_prepare_data.sh Movies_and_TV

# 2. 验证数据
python test_integration.py

# 3. 开始训练
python train_federated_llm_srec.py --dataset Movies_and_TV
```

### 完整数据集准备

```bash
# 准备所有推荐数据集
./scripts/download_and_prepare_data.sh Movies_and_TV Industrial_and_Scientific Video_Games

# 或者逐个准备
./scripts/download_and_prepare_data.sh Movies_and_TV
./scripts/download_and_prepare_data.sh Industrial_and_Scientific
./scripts/download_and_prepare_data.sh Video_Games
```

## 🔧 数据格式说明

### 统一后的数据格式

所有数据集处理后都会转换为统一格式：

```
data/
├── Movies_and_TV/
│   ├── train.txt          # 训练数据
│   ├── valid.txt          # 验证数据
│   ├── test.txt           # 测试数据
│   ├── item_meta.json     # 物品元数据
│   ├── user_map.json      # 用户ID映射
│   ├── item_map.json      # 物品ID映射
│   └── stats.json         # 统计信息
```

### 文件格式详解

**交互数据格式 (train.txt, valid.txt, test.txt):**
```
user_id item1 item2 item3 ...
1 15 23 45 67 89
2 12 34 56
...
```

**元数据格式 (item_meta.json):**
```json
{
  "1": {
    "title": "The Shawshank Redemption",
    "description": "Two imprisoned men bond over...",
    "categories": ["Drama", "Crime"]
  },
  "2": {
    "title": "The Godfather",
    "description": "The aging patriarch of...",
    "categories": ["Drama", "Crime"]
  }
}
```

## 📈 实验配置建议

### 数据集规模配置

| 数据集 | 推荐客户端数 | 联邦轮次 | 批次大小 | 预期训练时间 |
|--------|-------------|----------|----------|-------------|
| Movies_and_TV | 5-8 | 50-100 | 32 | 4-8小时 |
| Industrial_and_Scientific | 3-5 | 30-50 | 32 | 2-4小时 |
| Video_Games | 5-8 | 50-100 | 32 | 3-6小时 |

### 采样策略

为了控制实验规模和训练时间，建议使用以下采样比例：

```yaml
# 小规模测试
sample_ratios:
  Movies_and_TV: 0.1      # 10%数据，约5万交互
  Industrial_and_Scientific: 0.5  # 50%数据，约10万交互
  Video_Games: 0.2        # 20%数据，约10万交互

# 完整实验
sample_ratios:
  Movies_and_TV: 1.0      # 100%数据
  Industrial_and_Scientific: 1.0  # 100%数据
  Video_Games: 1.0        # 100%数据
```

## 🎛️ 配置文件调整

根据选择的数据集，需要调整配置文件：

```yaml
# configs/fedllm_srec_config.yaml
data:
  dataset: "Movies_and_TV"  # 或 "Industrial_and_Scientific", "Video_Games"
  maxlen: 128              # Movies_and_TV: 128, Industrial: 64, Games: 100
  item_num: 30000          # 根据实际数据集调整
  min_interactions: 5      # 最小交互次数

federated:
  client_num: 5            # Movies_and_TV: 5-8, Industrial: 3-5, Games: 5-8
  fed_rounds: 50           # 根据数据集大小调整
```

## 🔍 数据质量检查

### 自动检查脚本

```bash
# 检查数据质量
python data_preprocessing/check_data_quality.py --dataset Movies_and_TV

# 生成数据报告
python utils/generate_data_report.py --dataset Movies_and_TV
```

### 手动检查要点

1. **数据完整性**
   - 所有必要文件是否存在
   - 文件格式是否正确
   - ID映射是否一致

2. **数据分布**
   - 用户交互次数分布
   - 物品流行度分布
   - 序列长度分布

3. **文本质量**
   - 元数据完整性
   - 文本长度分布
   - 特殊字符处理

## 💡 最佳实践建议

### 1. 数据集选择策略

**初期实验：**
- 使用 Movies_and_TV (10%采样)
- 客户端数量：3-5
- 联邦轮次：10-20

**完整实验：**
- 主要数据集：Movies_and_TV (100%)
- 对比数据集：Industrial_and_Scientific
- 消融研究：Video_Games

### 2. 实验设计建议

```python
# 实验配置示例
experiments = [
    {
        "name": "baseline",
        "dataset": "Movies_and_TV",
        "sample_ratio": 0.1,
        "client_num": 3,
        "fed_rounds": 20
    },
    {
        "name": "full_scale",
        "dataset": "Movies_and_TV", 
        "sample_ratio": 1.0,
        "client_num": 5,
        "fed_rounds": 50
    },
    {
        "name": "cross_domain",
        "dataset": "Industrial_and_Scientific",
        "sample_ratio": 1.0,
        "client_num": 3,
        "fed_rounds": 30
    }
]
```

### 3. 性能优化建议

- **内存优化**：使用数据采样减少内存占用
- **计算优化**：根据GPU数量调整客户端数量
- **存储优化**：定期清理中间文件

## 🚀 开始使用

选择好数据集后，按以下步骤开始：

```bash
# 1. 准备数据
./scripts/download_and_prepare_data.sh Movies_and_TV

# 2. 运行测试
python test_integration.py

# 3. 开始训练
python train_federated_llm_srec.py \
    --dataset Movies_and_TV \
    --client_num 5 \
    --fed_rounds 50

# 4. 评估结果
python inference_fedllm_srec.py \
    --dataset Movies_and_TV \
    --model_dir ./models
```

这样您就可以开始使用与原项目一致且经过优化的数据集进行实验了！
