#!/bin/bash

# FedLLM-SRec 数据下载和准备脚本
# 支持多个Amazon数据集的自动下载和预处理

echo "=========================================="
echo "FedLLM-SRec 数据下载和准备脚本"
echo "=========================================="

# 设置基础路径
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
RAW_DATA_DIR="$BASE_DIR/raw_data"
PROCESSED_DATA_DIR="$BASE_DIR/data"

# 创建目录
mkdir -p "$RAW_DATA_DIR"
mkdir -p "$PROCESSED_DATA_DIR"

echo "基础目录: $BASE_DIR"
echo "原始数据目录: $RAW_DATA_DIR"
echo "处理后数据目录: $PROCESSED_DATA_DIR"

# 数据集配置
declare -A DATASETS
DATASETS["Movies_and_TV"]="2023"
DATASETS["Industrial_and_Scientific"]="2023"
DATASETS["Video_Games"]="2018"
DATASETS["Electronics"]="2023"
DATASETS["Books"]="2023"

# 采样比例配置（用于控制数据集大小）
declare -A SAMPLE_RATIOS
SAMPLE_RATIOS["Movies_and_TV"]=0.1
SAMPLE_RATIOS["Industrial_and_Scientific"]=1.0
SAMPLE_RATIOS["Video_Games"]=0.2
SAMPLE_RATIOS["Electronics"]=0.05
SAMPLE_RATIOS["Books"]=0.02

# 函数：下载Amazon 2023数据集
download_amazon_2023() {
    local dataset=$1
    echo "下载Amazon 2023数据集: $dataset"
    
    # 使用Python脚本下载（需要datasets库）
    python3 -c "
from datasets import load_dataset
import os

dataset_name = '$dataset'
output_dir = '$RAW_DATA_DIR'

print(f'下载数据集: {dataset_name}')

# 下载交互数据
try:
    dataset = load_dataset('McAuley-Lab/Amazon-Reviews-2023', f'5core_last_out_{dataset_name}')
    dataset.save_to_disk(os.path.join(output_dir, f'{dataset_name}_interactions'))
    print(f'交互数据下载完成: {dataset_name}')
except Exception as e:
    print(f'下载交互数据失败: {e}')

# 下载元数据
try:
    meta_dataset = load_dataset('McAuley-Lab/Amazon-Reviews-2023', f'raw_meta_{dataset_name}')
    meta_dataset.save_to_disk(os.path.join(output_dir, f'{dataset_name}_meta'))
    print(f'元数据下载完成: {dataset_name}')
except Exception as e:
    print(f'下载元数据失败: {e}')
"
}

# 函数：下载Amazon 2018数据集
download_amazon_2018() {
    local dataset=$1
    echo "下载Amazon 2018数据集: $dataset"
    
    # Amazon 2018数据集的下载链接
    local base_url="https://datarepo.eng.ucsd.edu/mcauley_group/data/amazon_v2"
    
    # 下载交互数据
    local interaction_file="${dataset}_5.json.gz"
    local interaction_url="$base_url/categoryFilesSmall/${interaction_file}"
    
    if [ ! -f "$RAW_DATA_DIR/$interaction_file" ]; then
        echo "下载交互数据: $interaction_file"
        wget -P "$RAW_DATA_DIR" "$interaction_url" || {
            echo "警告: 无法下载 $interaction_file，请手动下载"
        }
    else
        echo "交互数据已存在: $interaction_file"
    fi
    
    # 下载元数据
    local meta_file="meta_${dataset}.json.gz"
    local meta_url="$base_url/metaFiles2/${meta_file}"
    
    if [ ! -f "$RAW_DATA_DIR/$meta_file" ]; then
        echo "下载元数据: $meta_file"
        wget -P "$RAW_DATA_DIR" "$meta_url" || {
            echo "警告: 无法下载 $meta_file，请手动下载"
        }
    else
        echo "元数据已存在: $meta_file"
    fi
}

# 函数：处理数据集
process_dataset() {
    local dataset=$1
    local version=$2
    local sample_ratio=${SAMPLE_RATIOS[$dataset]}
    
    echo "处理数据集: $dataset (版本: $version, 采样比例: $sample_ratio)"
    
    local output_dir="$PROCESSED_DATA_DIR/$dataset"
    mkdir -p "$output_dir"
    
    # 运行数据预处理脚本
    python3 "$BASE_DIR/data_preprocessing/unified_data_processor.py" \
        --dataset "$dataset" \
        --data_dir "$RAW_DATA_DIR" \
        --output_dir "$output_dir" \
        --sample_ratio "$sample_ratio" \
        --data_version "$version"
    
    if [ $? -eq 0 ]; then
        echo "✓ 数据集 $dataset 处理完成"
        
        # 显示统计信息
        if [ -f "$output_dir/stats.json" ]; then
            echo "统计信息:"
            cat "$output_dir/stats.json"
        fi
    else
        echo "✗ 数据集 $dataset 处理失败"
    fi
}

# 函数：验证数据
validate_data() {
    local dataset=$1
    local data_dir="$PROCESSED_DATA_DIR/$dataset"
    
    echo "验证数据集: $dataset"
    
    # 检查必要文件
    local required_files=("train.txt" "valid.txt" "test.txt" "item_meta.json" "stats.json")
    local all_exist=true
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$data_dir/$file" ]; then
            echo "✗ 缺少文件: $file"
            all_exist=false
        else
            echo "✓ 文件存在: $file"
        fi
    done
    
    if [ "$all_exist" = true ]; then
        echo "✓ 数据集 $dataset 验证通过"
        
        # 显示数据统计
        echo "数据文件行数:"
        for split in train valid test; do
            if [ -f "$data_dir/${split}.txt" ]; then
                local line_count=$(wc -l < "$data_dir/${split}.txt")
                echo "  $split.txt: $line_count 行"
            fi
        done
    else
        echo "✗ 数据集 $dataset 验证失败"
    fi
}

# 主函数
main() {
    echo "开始数据下载和准备..."
    
    # 检查Python依赖
    echo "检查Python依赖..."
    python3 -c "import datasets, tqdm, numpy, pandas" 2>/dev/null || {
        echo "警告: 缺少必要的Python库，请安装："
        echo "pip install datasets tqdm numpy pandas"
    }
    
    # 处理命令行参数
    if [ $# -eq 0 ]; then
        echo "处理所有数据集..."
        datasets_to_process=("${!DATASETS[@]}")
    else
        echo "处理指定数据集: $@"
        datasets_to_process=("$@")
    fi
    
    # 下载和处理数据集
    for dataset in "${datasets_to_process[@]}"; do
        if [[ -v DATASETS[$dataset] ]]; then
            local version=${DATASETS[$dataset]}
            
            echo "=========================================="
            echo "处理数据集: $dataset"
            echo "=========================================="
            
            # 下载数据
            if [ "$version" = "2023" ]; then
                download_amazon_2023 "$dataset"
            else
                download_amazon_2018 "$dataset"
            fi
            
            # 处理数据
            process_dataset "$dataset" "$version"
            
            # 验证数据
            validate_data "$dataset"
            
        else
            echo "未知数据集: $dataset"
            echo "支持的数据集: ${!DATASETS[@]}"
        fi
    done
    
    echo "=========================================="
    echo "数据准备完成！"
    echo "=========================================="
    
    # 显示最终统计
    echo "已处理的数据集:"
    for dataset in "${datasets_to_process[@]}"; do
        if [ -d "$PROCESSED_DATA_DIR/$dataset" ]; then
            echo "✓ $dataset -> $PROCESSED_DATA_DIR/$dataset"
        fi
    done
    
    echo ""
    echo "使用方法:"
    echo "1. 训练模型:"
    echo "   python train_federated_llm_srec.py --dataset Movies_and_TV"
    echo ""
    echo "2. 运行测试:"
    echo "   python test_integration.py"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [数据集名称...]"
    echo ""
    echo "支持的数据集:"
    for dataset in "${!DATASETS[@]}"; do
        local version=${DATASETS[$dataset]}
        local ratio=${SAMPLE_RATIOS[$dataset]}
        echo "  $dataset (版本: $version, 采样比例: $ratio)"
    done
    echo ""
    echo "示例:"
    echo "  $0                          # 处理所有数据集"
    echo "  $0 Movies_and_TV           # 只处理Movies_and_TV数据集"
    echo "  $0 Movies_and_TV Video_Games # 处理多个数据集"
    echo ""
    echo "注意:"
    echo "1. 首次运行需要下载数据，可能需要较长时间"
    echo "2. 确保有足够的磁盘空间（每个数据集约1-5GB）"
    echo "3. 需要安装Python依赖: pip install datasets tqdm numpy pandas"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
