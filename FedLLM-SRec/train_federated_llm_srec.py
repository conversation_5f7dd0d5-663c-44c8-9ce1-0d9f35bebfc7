"""
FedLLM-SRec训练脚本
实现联邦学习大小模型协同推荐系统的完整训练流程

训练流程：
1. 阶段一：客户端CF-SRec预训练
2. 阶段二：联邦学习 + 知识蒸馏联合训练
3. 阶段三：模型评估和推理
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
import os
import json
from tqdm import tqdm
import logging
from typing import Dict, List, Tuple

from models.federated_llm_srec import FederatedLLMSRec
from utils.data_loader import FederatedDataLoader
from utils.evaluation import evaluate_model
from utils.logger import setup_logger

class FederatedLLMSRecTrainer:
    """联邦学习大小模型协同推荐系统训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device(args.device)
        self.logger = setup_logger(args.log_dir)
        
        # 初始化模型
        self.model = FederatedLLMSRec(args).to(self.device)
        
        # 初始化数据加载器
        self.data_loader = FederatedDataLoader(args)
        
        # 初始化优化器
        self.client_optimizers = self._init_client_optimizers()
        self.server_optimizer = self._init_server_optimizer()
        
        # 训练状态
        self.best_performance = 0.0
        self.patience_counter = 0
        
    def _init_client_optimizers(self) -> List[optim.Optimizer]:
        """初始化客户端优化器"""
        optimizers = []
        for client_model in self.model.client_models:
            optimizer = optim.Adam(
                client_model.parameters(),
                lr=self.args.client_lr,
                weight_decay=self.args.weight_decay
            )
            optimizers.append(optimizer)
        return optimizers
    
    def _init_server_optimizer(self) -> optim.Optimizer:
        """初始化服务器优化器"""
        return optim.Adam(
            self.model.server_llm.parameters(),
            lr=self.args.server_lr,
            weight_decay=self.args.weight_decay
        )
    
    def train(self):
        """主训练流程"""
        self.logger.info("开始联邦学习大小模型协同训练...")
        
        # 阶段一：客户端预训练
        if self.args.pretrain_clients:
            self.logger.info("阶段一：客户端CF-SRec预训练")
            self._pretrain_clients()
        
        # 阶段二：联邦学习 + 知识蒸馏
        self.logger.info("阶段二：联邦学习 + 知识蒸馏联合训练")
        self._federated_distillation_training()
        
        # 阶段三：最终评估
        self.logger.info("阶段三：最终模型评估")
        final_metrics = self._final_evaluation()
        
        self.logger.info(f"训练完成！最终性能: {final_metrics}")
        return final_metrics
    
    def _pretrain_clients(self):
        """客户端预训练阶段"""
        for epoch in range(self.args.pretrain_epochs):
            epoch_losses = []
            
            for client_id in range(self.args.client_num):
                # 获取客户端本地数据
                client_data = self.data_loader.get_client_data(client_id, 'train')
                
                # 客户端本地训练
                client_loss = self._train_single_client(client_id, client_data)
                epoch_losses.append(client_loss)
                
                self.logger.info(
                    f"预训练 Epoch {epoch+1}/{self.args.pretrain_epochs}, "
                    f"Client {client_id}, Loss: {client_loss:.4f}"
                )
            
            avg_loss = np.mean(epoch_losses)
            self.logger.info(f"预训练 Epoch {epoch+1} 平均损失: {avg_loss:.4f}")
            
            # 保存检查点
            if (epoch + 1) % self.args.save_interval == 0:
                self._save_checkpoint(epoch + 1, stage='pretrain')
    
    def _train_single_client(self, client_id: int, client_data: Dict) -> float:
        """单个客户端训练"""
        client_model = self.model.client_models[client_id]
        optimizer = self.client_optimizers[client_id]
        
        client_model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch in client_data['dataloader']:
            optimizer.zero_grad()
            
            user_seq, pos_items, neg_items = batch
            user_seq = user_seq.to(self.device)
            pos_items = pos_items.to(self.device)
            neg_items = neg_items.to(self.device)
            
            # 获取用户表示
            user_emb = client_model.get_user_embedding(user_seq)
            
            # 计算BPR损失
            pos_scores = client_model.predict(user_emb, pos_items)
            neg_scores = client_model.predict(user_emb, neg_items)
            
            bpr_loss = -torch.log(torch.sigmoid(pos_scores - neg_scores)).mean()
            
            bpr_loss.backward()
            optimizer.step()
            
            total_loss += bpr_loss.item()
            num_batches += 1
        
        return total_loss / num_batches if num_batches > 0 else 0.0
    
    def _federated_distillation_training(self):
        """联邦学习 + 知识蒸馏联合训练"""
        for round_idx in range(self.args.fed_rounds):
            self.logger.info(f"联邦学习轮次 {round_idx+1}/{self.args.fed_rounds}")
            
            # 1. 客户端本地训练
            client_results = []
            for client_id in range(self.args.client_num):
                client_data = self.data_loader.get_client_data(client_id, 'train')
                result = self.model.client_local_training(client_id, client_data)
                client_results.append(result)
            
            # 2. 服务器端知识蒸馏
            client_embeddings = [result['user_embeddings'] for result in client_results]
            text_inputs = self.data_loader.get_text_inputs(round_idx)
            
            distill_result = self.model.server_knowledge_distillation(
                client_embeddings, text_inputs
            )
            
            # 3. 联邦聚合
            aggregated_params = self.model.federated_aggregation(client_results)
            
            # 4. 更新客户端模型
            for client_id in range(self.args.client_num):
                self.model.client_models[client_id].load_state_dict(
                    aggregated_params[client_id]
                )
            
            # 5. 更新服务器模型
            distill_loss = distill_result['distillation_loss']
            self.server_optimizer.zero_grad()
            distill_loss.backward()
            self.server_optimizer.step()
            
            # 6. 记录训练状态
            avg_client_loss = np.mean([r['local_loss'] for r in client_results])
            self.logger.info(
                f"Round {round_idx+1}: "
                f"平均客户端损失: {avg_client_loss:.4f}, "
                f"蒸馏损失: {distill_loss.item():.4f}"
            )
            
            # 7. 验证和早停
            if (round_idx + 1) % self.args.eval_interval == 0:
                val_metrics = self._validate()
                self.logger.info(f"验证指标: {val_metrics}")
                
                # 早停检查
                if self._check_early_stopping(val_metrics):
                    self.logger.info("触发早停，训练结束")
                    break
                
                # 保存最佳模型
                if val_metrics['ndcg@10'] > self.best_performance:
                    self.best_performance = val_metrics['ndcg@10']
                    self._save_best_model()
                    self.patience_counter = 0
                else:
                    self.patience_counter += 1
    
    def _validate(self) -> Dict[str, float]:
        """验证模型性能"""
        self.model.eval()
        
        val_metrics = {'ndcg@10': 0.0, 'hit@10': 0.0, 'ndcg@20': 0.0, 'hit@20': 0.0}
        total_users = 0
        
        with torch.no_grad():
            for client_id in range(self.args.client_num):
                client_data = self.data_loader.get_client_data(client_id, 'valid')
                
                # 客户端推理
                client_metrics = self._evaluate_client(client_id, client_data)
                
                # 累积指标
                for metric in val_metrics:
                    val_metrics[metric] += client_metrics[metric] * client_metrics['num_users']
                
                total_users += client_metrics['num_users']
        
        # 计算平均指标
        for metric in val_metrics:
            val_metrics[metric] /= total_users if total_users > 0 else 1
        
        self.model.train()
        return val_metrics
    
    def _evaluate_client(self, client_id: int, client_data: Dict) -> Dict[str, float]:
        """评估单个客户端"""
        client_model = self.model.client_models[client_id]
        
        metrics = evaluate_model(
            client_model, 
            client_data, 
            self.device,
            top_k=[10, 20]
        )
        
        return metrics
    
    def _check_early_stopping(self, val_metrics: Dict[str, float]) -> bool:
        """检查是否触发早停"""
        return self.patience_counter >= self.args.patience
    
    def _save_checkpoint(self, epoch: int, stage: str = 'federated'):
        """保存训练检查点"""
        checkpoint = {
            'epoch': epoch,
            'stage': stage,
            'model_state_dict': self.model.state_dict(),
            'client_optimizers': [opt.state_dict() for opt in self.client_optimizers],
            'server_optimizer': self.server_optimizer.state_dict(),
            'best_performance': self.best_performance,
            'args': self.args
        }
        
        checkpoint_path = os.path.join(
            self.args.checkpoint_dir, 
            f'checkpoint_{stage}_epoch_{epoch}.pt'
        )
        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"检查点已保存: {checkpoint_path}")
    
    def _save_best_model(self):
        """保存最佳模型"""
        best_model_path = os.path.join(self.args.model_dir, 'best_model.pt')
        torch.save(self.model.state_dict(), best_model_path)
        self.logger.info(f"最佳模型已保存: {best_model_path}")
    
    def _final_evaluation(self) -> Dict[str, float]:
        """最终评估"""
        # 加载最佳模型
        best_model_path = os.path.join(self.args.model_dir, 'best_model.pt')
        if os.path.exists(best_model_path):
            self.model.load_state_dict(torch.load(best_model_path))
            self.logger.info("已加载最佳模型进行最终评估")
        
        # 在测试集上评估
        test_metrics = {'ndcg@10': 0.0, 'hit@10': 0.0, 'ndcg@20': 0.0, 'hit@20': 0.0}
        total_users = 0
        
        self.model.eval()
        with torch.no_grad():
            for client_id in range(self.args.client_num):
                client_data = self.data_loader.get_client_data(client_id, 'test')
                client_metrics = self._evaluate_client(client_id, client_data)
                
                for metric in test_metrics:
                    test_metrics[metric] += client_metrics[metric] * client_metrics['num_users']
                
                total_users += client_metrics['num_users']
        
        # 计算平均指标
        for metric in test_metrics:
            test_metrics[metric] /= total_users if total_users > 0 else 1
        
        return test_metrics


def main():
    parser = argparse.ArgumentParser(description='FedLLM-SRec训练')
    
    # 基础配置
    parser.add_argument('--device', type=str, default='cuda:0')
    parser.add_argument('--seed', type=int, default=42)
    parser.add_argument('--log_dir', type=str, default='./logs')
    parser.add_argument('--model_dir', type=str, default='./models')
    parser.add_argument('--checkpoint_dir', type=str, default='./checkpoints')
    
    # 数据配置
    parser.add_argument('--data_dir', type=str, default='./data')
    parser.add_argument('--dataset', type=str, default='Movies_and_TV')
    parser.add_argument('--maxlen', type=int, default=128)
    parser.add_argument('--item_num', type=int, default=10000)
    
    # 模型配置
    parser.add_argument('--hidden_units', type=int, default=256)
    parser.add_argument('--dropout_rate', type=float, default=0.2)
    parser.add_argument('--base_model', type=str, default='meta-llama/Llama-2-7b-hf')
    
    # LoRA配置
    parser.add_argument('--lora_r', type=int, default=8)
    parser.add_argument('--lora_alpha', type=int, default=16)
    parser.add_argument('--lora_dropout', type=float, default=0.05)
    parser.add_argument('--lora_target_modules', type=list, default=['q_proj', 'v_proj'])
    
    # 联邦学习配置
    parser.add_argument('--client_num', type=int, default=5)
    parser.add_argument('--fed_rounds', type=int, default=50)
    parser.add_argument('--similarity_threshold', type=float, default=0.5)
    parser.add_argument('--alpha', type=float, default=0.7)
    parser.add_argument('--beta', type=int, default=1)
    
    # 训练配置
    parser.add_argument('--pretrain_clients', action='store_true')
    parser.add_argument('--pretrain_epochs', type=int, default=10)
    parser.add_argument('--client_lr', type=float, default=1e-3)
    parser.add_argument('--server_lr', type=float, default=1e-4)
    parser.add_argument('--weight_decay', type=float, default=1e-5)
    parser.add_argument('--uniformity_weight', type=float, default=0.1)
    
    # 评估配置
    parser.add_argument('--eval_interval', type=int, default=5)
    parser.add_argument('--patience', type=int, default=10)
    parser.add_argument('--save_interval', type=int, default=10)
    
    args = parser.parse_args()
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 创建必要的目录
    os.makedirs(args.log_dir, exist_ok=True)
    os.makedirs(args.model_dir, exist_ok=True)
    os.makedirs(args.checkpoint_dir, exist_ok=True)
    
    # 开始训练
    trainer = FederatedLLMSRecTrainer(args)
    final_metrics = trainer.train()
    
    print(f"训练完成！最终测试指标: {final_metrics}")


if __name__ == '__main__':
    main()
