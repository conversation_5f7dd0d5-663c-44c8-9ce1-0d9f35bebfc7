"""
FedLLM-SRec: 联邦学习大小模型协同推荐系统
融合FELLRec的联邦学习框架和LLM-SRec的大小模型协同机制

主要创新点：
1. 在联邦学习框架中集成知识蒸馏机制
2. 客户端部署CF-SRec小模型，服务器部署LLM大模型
3. 保持隐私保护的同时实现大小模型协同
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import LlamaForCausalLM, AutoTokenizer
from peft import LoraConfig, get_peft_model, prepare_model_for_int8_training
import numpy as np
from typing import Dict, List, Tuple, Optional

class FederatedLLMSRec(nn.Module):
    """
    联邦学习大小模型协同推荐系统主模型
    
    架构特点：
    - 客户端：CF-SRec小模型 + LoRA微调
    - 服务器：LLM大模型 + 知识蒸馏
    - 联邦聚合：基于相似度的智能聚合
    """
    
    def __init__(self, args):
        super().__init__()
        self.args = args
        self.device = args.device
        self.client_num = args.client_num
        
        # 初始化客户端CF-SRec模型
        self.client_models = self._init_client_models()
        
        # 初始化服务器LLM模型
        self.server_llm = self._init_server_llm()
        
        # 知识蒸馏相关组件
        self.distillation_loss = nn.MSELoss()
        self.uniformity_loss = self._uniformity_loss
        
        # 联邦聚合相关
        self.similarity_threshold = args.similarity_threshold
        self.aggregation_weights = None
        
    def _init_client_models(self) -> List[nn.Module]:
        """初始化客户端CF-SRec模型"""
        client_models = []
        
        for i in range(self.client_num):
            # 基于SASRec的序列推荐模型
            client_model = ClientCFSRec(
                item_num=self.args.item_num,
                hidden_units=self.args.hidden_units,
                maxlen=self.args.maxlen,
                dropout_rate=self.args.dropout_rate,
                device=self.device
            )
            client_models.append(client_model)
            
        return client_models
    
    def _init_server_llm(self) -> nn.Module:
        """初始化服务器LLM模型"""
        # 加载LLaMA模型
        llm_model = LlamaForCausalLM.from_pretrained(
            self.args.base_model,
            load_in_8bit=True,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        # 准备LoRA微调
        llm_model = prepare_model_for_int8_training(llm_model)
        
        # 配置LoRA
        lora_config = LoraConfig(
            r=self.args.lora_r,
            lora_alpha=self.args.lora_alpha,
            target_modules=self.args.lora_target_modules,
            lora_dropout=self.args.lora_dropout,
            bias="none",
            task_type="CAUSAL_LM"
        )
        
        llm_model = get_peft_model(llm_model, lora_config)
        
        return llm_model
    
    def client_local_training(self, client_id: int, local_data: Dict) -> Dict:
        """
        客户端本地训练
        
        Args:
            client_id: 客户端ID
            local_data: 本地训练数据
            
        Returns:
            训练后的模型参数和用户表示
        """
        client_model = self.client_models[client_id]
        client_model.train()
        
        # 提取用户序列特征
        user_seq = local_data['user_sequences']
        user_emb = client_model.get_user_embedding(user_seq)
        
        # 计算推荐损失
        pos_items = local_data['pos_items']
        neg_items = local_data['neg_items']
        
        pos_scores = client_model.predict(user_emb, pos_items)
        neg_scores = client_model.predict(user_emb, neg_items)
        
        # BPR损失
        bpr_loss = -torch.log(torch.sigmoid(pos_scores - neg_scores)).mean()
        
        return {
            'model_params': client_model.state_dict(),
            'user_embeddings': user_emb,
            'local_loss': bpr_loss.item()
        }
    
    def server_knowledge_distillation(self, client_embeddings: List[torch.Tensor], 
                                    text_inputs: List[str]) -> Dict:
        """
        服务器端知识蒸馏
        
        Args:
            client_embeddings: 客户端用户表示
            text_inputs: 文本输入
            
        Returns:
            蒸馏损失和LLM表示
        """
        self.server_llm.train()
        
        # LLM生成用户表示
        llm_embeddings = self._get_llm_embeddings(text_inputs)
        
        # 知识蒸馏损失
        distill_losses = []
        for client_emb, llm_emb in zip(client_embeddings, llm_embeddings):
            # 归一化表示
            client_emb_norm = F.normalize(client_emb, p=2, dim=1)
            llm_emb_norm = F.normalize(llm_emb, p=2, dim=1)
            
            # 计算蒸馏损失
            distill_loss = self.distillation_loss(llm_emb_norm, client_emb_norm)
            
            # 添加均匀性正则化
            uniformity_loss = (self.uniformity_loss(llm_emb_norm) + 
                             self.uniformity_loss(client_emb_norm))
            
            total_loss = distill_loss + self.args.uniformity_weight * uniformity_loss
            distill_losses.append(total_loss)
        
        avg_distill_loss = torch.stack(distill_losses).mean()
        
        return {
            'distillation_loss': avg_distill_loss,
            'llm_embeddings': llm_embeddings
        }
    
    def federated_aggregation(self, client_params: List[Dict]) -> Dict:
        """
        联邦聚合
        
        Args:
            client_params: 客户端模型参数列表
            
        Returns:
            聚合后的全局模型参数
        """
        # 计算客户端相似度矩阵
        similarity_matrix = self._compute_similarity_matrix(client_params)
        
        # 基于相似度的智能聚合
        aggregated_params = {}
        
        for client_id in range(self.client_num):
            client_aggregated = {}
            
            for param_name in client_params[0]['model_params'].keys():
                weighted_param = torch.zeros_like(
                    client_params[client_id]['model_params'][param_name]
                )
                
                total_weight = 0
                for other_id in range(self.client_num):
                    similarity = similarity_matrix[client_id][other_id]
                    if similarity > self.similarity_threshold:
                        weight = similarity * self._get_dynamic_weight(
                            client_id, other_id, client_params
                        )
                        weighted_param += (weight * 
                                         client_params[other_id]['model_params'][param_name])
                        total_weight += weight
                
                if total_weight > 0:
                    client_aggregated[param_name] = weighted_param / total_weight
                else:
                    client_aggregated[param_name] = client_params[client_id]['model_params'][param_name]
            
            aggregated_params[client_id] = client_aggregated
        
        return aggregated_params
    
    def _compute_similarity_matrix(self, client_params: List[Dict]) -> torch.Tensor:
        """计算客户端相似度矩阵"""
        similarity_matrix = torch.zeros(self.client_num, self.client_num)
        
        for i in range(self.client_num):
            for j in range(self.client_num):
                if i != j:
                    # 计算参数余弦相似度
                    sim = self._cosine_similarity(
                        client_params[i]['user_embeddings'],
                        client_params[j]['user_embeddings']
                    )
                    similarity_matrix[i][j] = sim
                else:
                    similarity_matrix[i][j] = 1.0
        
        return similarity_matrix
    
    def _cosine_similarity(self, emb1: torch.Tensor, emb2: torch.Tensor) -> float:
        """计算余弦相似度"""
        emb1_flat = emb1.view(-1)
        emb2_flat = emb2.view(-1)
        return F.cosine_similarity(emb1_flat.unsqueeze(0), emb2_flat.unsqueeze(0)).item()
    
    def _get_dynamic_weight(self, client_id: int, other_id: int, 
                          client_params: List[Dict]) -> float:
        """计算动态聚合权重"""
        client_loss = client_params[client_id]['local_loss']
        other_loss = client_params[other_id]['local_loss']
        
        # 基于损失的动态权重
        weight = torch.tanh(self.args.alpha / (other_loss ** (self.args.beta)))
        return weight.item()
    
    def _get_llm_embeddings(self, text_inputs: List[str]) -> List[torch.Tensor]:
        """获取LLM用户表示"""
        # 这里需要实现LLM的文本编码逻辑
        # 类似于LLM-SRec中的实现
        pass
    
    def _uniformity_loss(self, embeddings: torch.Tensor) -> torch.Tensor:
        """均匀性损失，防止表示坍塌"""
        embeddings = F.normalize(embeddings, p=2, dim=1)
        similarity_matrix = torch.matmul(embeddings, embeddings.t())
        uniformity = torch.log(torch.exp(similarity_matrix).sum(dim=1)).mean()
        return uniformity


class ClientCFSRec(nn.Module):
    """客户端CF-SRec模型（基于SASRec）"""
    
    def __init__(self, item_num: int, hidden_units: int, maxlen: int, 
                 dropout_rate: float, device: str):
        super().__init__()
        self.item_num = item_num
        self.hidden_units = hidden_units
        self.maxlen = maxlen
        self.device = device
        
        # 物品嵌入层
        self.item_emb = nn.Embedding(item_num + 1, hidden_units, padding_idx=0)
        self.pos_emb = nn.Embedding(maxlen, hidden_units)
        
        # Transformer编码器
        self.attention_layers = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=hidden_units,
                nhead=8,
                dropout=dropout_rate,
                batch_first=True
            ) for _ in range(2)
        ])
        
        self.dropout = nn.Dropout(dropout_rate)
        self.layer_norm = nn.LayerNorm(hidden_units)
        
    def get_user_embedding(self, user_seq: torch.Tensor) -> torch.Tensor:
        """获取用户序列表示"""
        seq_len = user_seq.size(1)
        pos_ids = torch.arange(seq_len, device=self.device).unsqueeze(0)
        
        # 嵌入
        item_embs = self.item_emb(user_seq)
        pos_embs = self.pos_emb(pos_ids)
        
        # 序列表示
        seq_embs = item_embs + pos_embs
        seq_embs = self.dropout(seq_embs)
        
        # Transformer编码
        for layer in self.attention_layers:
            seq_embs = layer(seq_embs)
        
        # 取最后一个位置的表示作为用户表示
        user_emb = seq_embs[:, -1, :]
        return self.layer_norm(user_emb)
    
    def predict(self, user_emb: torch.Tensor, item_ids: torch.Tensor) -> torch.Tensor:
        """预测用户对物品的偏好分数"""
        item_embs = self.item_emb(item_ids)
        scores = torch.sum(user_emb.unsqueeze(1) * item_embs, dim=-1)
        return scores
