# FedLLM-SRec 配置文件
# 联邦学习大小模型协同推荐系统配置

# 基础配置
basic:
  device: "cuda:0"
  seed: 42
  experiment_name: "fedllm_srec_movies_tv"
  
# 路径配置
paths:
  data_dir: "./data"
  log_dir: "./logs"
  model_dir: "./models"
  checkpoint_dir: "./checkpoints"
  output_dir: "./outputs"

# 数据配置
data:
  dataset: "Movies_and_TV"
  maxlen: 128
  item_num: 10000
  min_interactions: 5
  test_ratio: 0.2
  valid_ratio: 0.1

# 模型配置
model:
  # CF-SRec (客户端小模型)
  cf_srec:
    hidden_units: 256
    num_layers: 2
    num_heads: 8
    dropout_rate: 0.2
    activation: "gelu"
  
  # LLM (服务器大模型)
  llm:
    base_model: "meta-llama/Llama-2-7b-hf"
    load_in_8bit: true
    torch_dtype: "float16"
    
  # LoRA配置
  lora:
    r: 8
    alpha: 16
    dropout: 0.05
    target_modules: ["q_proj", "v_proj", "k_proj", "o_proj"]
    bias: "none"
    task_type: "CAUSAL_LM"

# 联邦学习配置
federated:
  client_num: 5
  fed_rounds: 50
  local_epochs: 1
  
  # 聚合策略
  aggregation:
    similarity_threshold: 0.5
    alpha: 0.7  # 聚合权重参数
    beta: 1     # 动态权重参数
    
  # 客户端选择
  client_selection:
    strategy: "random"  # random, loss_based, similarity_based
    selection_ratio: 1.0

# 知识蒸馏配置
distillation:
  temperature: 4.0
  alpha: 0.7  # 蒸馏损失权重
  beta: 0.3   # 任务损失权重
  
  # 损失函数
  distill_loss: "mse"  # mse, kl_div, cosine
  uniformity_weight: 0.1
  
  # 表示对齐
  alignment:
    projection_dim: 256
    normalize: true

# 训练配置
training:
  # 预训练阶段
  pretrain:
    enabled: true
    epochs: 10
    batch_size: 64
    learning_rate: 1e-3
    weight_decay: 1e-5
    
  # 联邦训练阶段
  federated:
    client_lr: 1e-3
    server_lr: 1e-4
    batch_size: 32
    weight_decay: 1e-5
    grad_clip: 1.0
    
  # 优化器
  optimizer:
    type: "adam"
    betas: [0.9, 0.999]
    eps: 1e-8
    
  # 学习率调度
  scheduler:
    type: "cosine"  # cosine, linear, exponential
    warmup_steps: 100
    min_lr: 1e-6

# 评估配置
evaluation:
  metrics: ["ndcg@10", "hit@10", "ndcg@20", "hit@20"]
  eval_interval: 5
  batch_size: 64
  
  # 早停
  early_stopping:
    patience: 10
    min_delta: 1e-4
    monitor: "ndcg@10"
    mode: "max"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_interval: 10
  
  # Wandb配置
  wandb:
    enabled: false
    project: "fedllm-srec"
    entity: "your_entity"

# 保存配置
saving:
  save_interval: 10
  keep_best_only: true
  save_optimizer: false
  
# 推理配置
inference:
  batch_size: 128
  top_k: [10, 20, 50]
  temperature: 0.0
  do_sample: false
  
# 硬件配置
hardware:
  mixed_precision: true
  gradient_checkpointing: false
  dataloader_num_workers: 4
  pin_memory: true

# 实验配置
experiment:
  # 消融研究
  ablation:
    no_distillation: false
    no_federation: false
    no_similarity_aggregation: false
    
  # 对比基线
  baselines:
    - "fedavg"
    - "fedprox"
    - "scaffold"
    - "llm4rec"
    - "sasrec"
