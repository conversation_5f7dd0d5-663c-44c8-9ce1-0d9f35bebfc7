"""
FedLLM-SRec 集成测试脚本
验证系统各组件的正确性和兼容性
"""

import torch
import numpy as np
import unittest
import tempfile
import os
from unittest.mock import Mock, patch

from models.federated_llm_srec import FederatedLLMSRec, ClientCFSRec
from utils.data_loader import FederatedDataLoader
from train_federated_llm_srec import FederatedLLMSRecTrainer

class TestFedLLMSRec(unittest.TestCase):
    """FedLLM-SRec 集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建测试参数
        self.args = Mock()
        self.args.device = self.device
        self.args.client_num = 3
        self.args.item_num = 1000
        self.args.hidden_units = 64
        self.args.maxlen = 50
        self.args.dropout_rate = 0.1
        self.args.base_model = "meta-llama/Llama-2-7b-hf"
        self.args.lora_r = 4
        self.args.lora_alpha = 8
        self.args.lora_target_modules = ['q_proj', 'v_proj']
        self.args.lora_dropout = 0.05
        self.args.similarity_threshold = 0.5
        self.args.alpha = 0.7
        self.args.beta = 1
        self.args.uniformity_weight = 0.1
        self.args.batch_size = 16
        
    def test_client_model_initialization(self):
        """测试客户端模型初始化"""
        print("测试客户端模型初始化...")
        
        client_model = ClientCFSRec(
            item_num=self.args.item_num,
            hidden_units=self.args.hidden_units,
            maxlen=self.args.maxlen,
            dropout_rate=self.args.dropout_rate,
            device=str(self.device)
        )
        
        # 测试前向传播
        batch_size = 8
        user_seq = torch.randint(1, self.args.item_num, (batch_size, self.args.maxlen))
        user_seq = user_seq.to(self.device)
        
        user_emb = client_model.get_user_embedding(user_seq)
        
        self.assertEqual(user_emb.shape, (batch_size, self.args.hidden_units))
        self.assertFalse(torch.isnan(user_emb).any())
        
        print("✓ 客户端模型初始化测试通过")
    
    def test_federated_model_components(self):
        """测试联邦模型组件"""
        print("测试联邦模型组件...")
        
        # 模拟LLM初始化（避免实际加载大模型）
        with patch('models.federated_llm_srec.LlamaForCausalLM') as mock_llm:
            mock_model = Mock()
            mock_llm.from_pretrained.return_value = mock_model
            
            with patch('models.federated_llm_srec.prepare_model_for_int8_training') as mock_prepare:
                mock_prepare.return_value = mock_model
                
                with patch('models.federated_llm_srec.get_peft_model') as mock_peft:
                    mock_peft.return_value = mock_model
                    
                    # 创建联邦模型
                    fed_model = FederatedLLMSRec(self.args)
                    
                    # 验证客户端模型数量
                    self.assertEqual(len(fed_model.client_models), self.args.client_num)
                    
                    # 验证服务器模型
                    self.assertIsNotNone(fed_model.server_llm)
        
        print("✓ 联邦模型组件测试通过")
    
    def test_similarity_computation(self):
        """测试相似度计算"""
        print("测试相似度计算...")
        
        with patch('models.federated_llm_srec.LlamaForCausalLM'):
            with patch('models.federated_llm_srec.prepare_model_for_int8_training'):
                with patch('models.federated_llm_srec.get_peft_model'):
                    fed_model = FederatedLLMSRec(self.args)
                    
                    # 创建模拟客户端参数
                    client_params = []
                    for i in range(self.args.client_num):
                        user_emb = torch.randn(10, self.args.hidden_units)
                        client_params.append({
                            'user_embeddings': user_emb,
                            'local_loss': np.random.random(),
                            'model_params': {'dummy': torch.randn(5, 5)}
                        })
                    
                    # 计算相似度矩阵
                    sim_matrix = fed_model._compute_similarity_matrix(client_params)
                    
                    # 验证相似度矩阵形状和性质
                    self.assertEqual(sim_matrix.shape, (self.args.client_num, self.args.client_num))
                    
                    # 对角线应该为1
                    for i in range(self.args.client_num):
                        self.assertAlmostEqual(sim_matrix[i][i].item(), 1.0, places=5)
                    
                    # 相似度应该在[-1, 1]范围内
                    self.assertTrue(torch.all(sim_matrix >= -1))
                    self.assertTrue(torch.all(sim_matrix <= 1))
        
        print("✓ 相似度计算测试通过")
    
    def test_data_loader_functionality(self):
        """测试数据加载器功能"""
        print("测试数据加载器功能...")
        
        # 创建临时数据目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建模拟数据文件
            dataset_dir = os.path.join(temp_dir, "test_dataset")
            os.makedirs(dataset_dir, exist_ok=True)
            
            # 创建训练数据文件
            train_file = os.path.join(dataset_dir, "train.txt")
            with open(train_file, 'w') as f:
                for user_id in range(1, 21):  # 20个用户
                    items = [str(i) for i in range(1, 11)]  # 每个用户10个物品
                    f.write(f"{user_id} {' '.join(items)}\n")
            
            # 创建验证和测试文件
            for split in ['valid', 'test']:
                split_file = os.path.join(dataset_dir, f"{split}.txt")
                with open(split_file, 'w') as f:
                    for user_id in range(1, 21):
                        items = [str(i) for i in range(11, 16)]  # 每个用户5个物品
                        f.write(f"{user_id} {' '.join(items)}\n")
            
            # 更新参数
            self.args.data_dir = temp_dir
            self.args.dataset = "test_dataset"
            
            # 创建数据加载器
            data_loader = FederatedDataLoader(self.args)
            
            # 验证数据分割
            stats = data_loader.get_statistics()
            self.assertEqual(len(stats['clients']), self.args.client_num)
            self.assertGreater(stats['total_users'], 0)
            
            # 验证客户端数据获取
            for client_id in range(self.args.client_num):
                client_data = data_loader.get_client_data(client_id, 'train')
                self.assertIsNotNone(client_data['dataloader'])
                self.assertGreaterEqual(client_data['num_users'], 0)
        
        print("✓ 数据加载器功能测试通过")
    
    def test_training_step_simulation(self):
        """测试训练步骤模拟"""
        print("测试训练步骤模拟...")
        
        with patch('models.federated_llm_srec.LlamaForCausalLM'):
            with patch('models.federated_llm_srec.prepare_model_for_int8_training'):
                with patch('models.federated_llm_srec.get_peft_model'):
                    fed_model = FederatedLLMSRec(self.args)
                    
                    # 模拟客户端本地训练数据
                    batch_size = 8
                    local_data = {
                        'user_sequences': torch.randint(1, self.args.item_num, 
                                                      (batch_size, self.args.maxlen)),
                        'pos_items': torch.randint(1, self.args.item_num, (batch_size,)),
                        'neg_items': torch.randint(1, self.args.item_num, (batch_size,))
                    }
                    
                    # 执行客户端本地训练
                    client_id = 0
                    result = fed_model.client_local_training(client_id, local_data)
                    
                    # 验证返回结果
                    self.assertIn('model_params', result)
                    self.assertIn('user_embeddings', result)
                    self.assertIn('local_loss', result)
                    
                    # 验证用户表示形状
                    user_emb = result['user_embeddings']
                    self.assertEqual(user_emb.shape, (batch_size, self.args.hidden_units))
        
        print("✓ 训练步骤模拟测试通过")
    
    def test_loss_computation(self):
        """测试损失计算"""
        print("测试损失计算...")
        
        # 测试BPR损失
        batch_size = 16
        pos_scores = torch.randn(batch_size)
        neg_scores = torch.randn(batch_size)
        
        bpr_loss = -torch.log(torch.sigmoid(pos_scores - neg_scores)).mean()
        
        self.assertFalse(torch.isnan(bpr_loss))
        self.assertGreater(bpr_loss.item(), 0)
        
        # 测试MSE蒸馏损失
        emb1 = torch.randn(batch_size, 64)
        emb2 = torch.randn(batch_size, 64)
        
        emb1_norm = torch.nn.functional.normalize(emb1, p=2, dim=1)
        emb2_norm = torch.nn.functional.normalize(emb2, p=2, dim=1)
        
        mse_loss = torch.nn.functional.mse_loss(emb1_norm, emb2_norm)
        
        self.assertFalse(torch.isnan(mse_loss))
        self.assertGreaterEqual(mse_loss.item(), 0)
        
        print("✓ 损失计算测试通过")
    
    def test_model_saving_loading(self):
        """测试模型保存和加载"""
        print("测试模型保存和加载...")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch('models.federated_llm_srec.LlamaForCausalLM'):
                with patch('models.federated_llm_srec.prepare_model_for_int8_training'):
                    with patch('models.federated_llm_srec.get_peft_model'):
                        # 创建模型
                        fed_model = FederatedLLMSRec(self.args)
                        
                        # 保存模型状态
                        model_path = os.path.join(temp_dir, "test_model.pt")
                        torch.save(fed_model.state_dict(), model_path)
                        
                        # 验证文件存在
                        self.assertTrue(os.path.exists(model_path))
                        
                        # 加载模型状态
                        state_dict = torch.load(model_path, map_location='cpu')
                        self.assertIsInstance(state_dict, dict)
        
        print("✓ 模型保存和加载测试通过")

def run_integration_tests():
    """运行集成测试"""
    print("=" * 60)
    print("FedLLM-SRec 集成测试开始")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestFedLLMSRec)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 60)
    if result.wasSuccessful():
        print("✅ 所有集成测试通过！")
        print("系统各组件工作正常，可以开始正式训练。")
    else:
        print("❌ 部分测试失败！")
        print(f"失败测试数量: {len(result.failures)}")
        print(f"错误测试数量: {len(result.errors)}")
        
        for test, error in result.failures + result.errors:
            print(f"失败测试: {test}")
            print(f"错误信息: {error}")
    
    print("=" * 60)
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_integration_tests()
    exit(0 if success else 1)
