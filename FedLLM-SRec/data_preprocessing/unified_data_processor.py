"""
统一数据预处理器
将FELLRec和LLM-SRec的数据格式统一为FedLLM-SRec所需的格式

支持的数据集：
1. Movies_and_TV (Amazon Reviews 2023)
2. Industrial_and_Scientific (Amazon Reviews 2023)  
3. Video_Games (Amazon Reviews 2018)
4. Electronics, Books, Beauty等其他Amazon数据集
"""

import os
import json
import gzip
import pickle
import numpy as np
import pandas as pd
from collections import defaultdict
from tqdm import tqdm
import random
from typing import Dict, List, Tuple
import argparse

class UnifiedDataProcessor:
    """统一数据预处理器"""
    
    def __init__(self, dataset_name: str, data_dir: str, output_dir: str):
        self.dataset_name = dataset_name
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.min_interactions = 5
        self.min_item_interactions = 5
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
    def process_amazon_2023_dataset(self, sample_ratio: float = 1.0):
        """
        处理Amazon Reviews 2023数据集 (Movies_and_TV, Industrial_and_Scientific等)
        
        Args:
            sample_ratio: 采样比例，用于控制数据集大小
        """
        print(f"处理Amazon 2023数据集: {self.dataset_name}")
        
        # 加载数据
        interactions, item_meta = self._load_amazon_2023_data()
        
        # 数据过滤和采样
        filtered_data = self._filter_and_sample_data(interactions, sample_ratio)
        
        # 重新映射ID
        processed_data, user_map, item_map = self._remap_ids(filtered_data)
        
        # 分割数据集
        train_data, valid_data, test_data = self._split_dataset(processed_data)
        
        # 保存处理后的数据
        self._save_processed_data(train_data, valid_data, test_data, 
                                item_meta, user_map, item_map)
        
        print(f"数据处理完成！输出目录: {self.output_dir}")
        
    def process_amazon_2018_dataset(self, sample_ratio: float = 1.0):
        """
        处理Amazon Reviews 2018数据集 (Video_Games等)
        
        Args:
            sample_ratio: 采样比例
        """
        print(f"处理Amazon 2018数据集: {self.dataset_name}")
        
        # 加载数据
        interactions, item_meta = self._load_amazon_2018_data()
        
        # 数据过滤和采样
        filtered_data = self._filter_and_sample_data(interactions, sample_ratio)
        
        # 重新映射ID
        processed_data, user_map, item_map = self._remap_ids(filtered_data)
        
        # 分割数据集
        train_data, valid_data, test_data = self._split_dataset(processed_data)
        
        # 保存处理后的数据
        self._save_processed_data(train_data, valid_data, test_data, 
                                item_meta, user_map, item_map)
        
        print(f"数据处理完成！输出目录: {self.output_dir}")
    
    def _load_amazon_2023_data(self) -> Tuple[Dict, Dict]:
        """加载Amazon 2023数据"""
        try:
            from datasets import load_dataset
            
            # 加载交互数据
            print("加载交互数据...")
            dataset = load_dataset("McAuley-Lab/Amazon-Reviews-2023", 
                                 f"5core_last_out_{self.dataset_name}")
            
            # 加载元数据
            print("加载元数据...")
            meta_dataset = load_dataset("McAuley-Lab/Amazon-Reviews-2023", 
                                      f"raw_meta_{self.dataset_name}")
            
            # 处理元数据
            item_meta = {}
            for item in tqdm(meta_dataset['full'], desc="处理元数据"):
                if 'parent_asin' in item:
                    item_meta[item['parent_asin']] = {
                        'title': item.get('title', f"Item_{item['parent_asin']}"),
                        'description': item.get('description', [''])[0] if item.get('description') else '',
                        'categories': item.get('categories', [])
                    }
            
            # 处理交互数据
            interactions = defaultdict(list)
            for split in ['train', 'valid', 'test']:
                for interaction in tqdm(dataset[split], desc=f"处理{split}数据"):
                    user_id = interaction['user_id']
                    item_id = interaction['parent_asin']
                    timestamp = interaction.get('timestamp', 0)
                    rating = interaction.get('rating', 5.0)
                    
                    interactions[user_id].append({
                        'item_id': item_id,
                        'timestamp': timestamp,
                        'rating': rating,
                        'split': split
                    })
            
            return dict(interactions), item_meta
            
        except ImportError:
            print("未安装datasets库，尝试从本地文件加载...")
            return self._load_local_amazon_data()
    
    def _load_amazon_2018_data(self) -> Tuple[Dict, Dict]:
        """加载Amazon 2018数据"""
        interactions = defaultdict(list)
        item_meta = {}
        
        # 加载交互数据
        interaction_file = os.path.join(self.data_dir, f"{self.dataset_name}_5.json.gz")
        if os.path.exists(interaction_file):
            print(f"加载交互数据: {interaction_file}")
            with gzip.open(interaction_file, 'r') as f:
                for line in tqdm(f, desc="处理交互数据"):
                    data = json.loads(line)
                    user_id = data['reviewerID']
                    item_id = data['asin']
                    timestamp = data.get('unixReviewTime', 0)
                    rating = data.get('overall', 5.0)
                    
                    interactions[user_id].append({
                        'item_id': item_id,
                        'timestamp': timestamp,
                        'rating': rating
                    })
        
        # 加载元数据
        meta_file = os.path.join(self.data_dir, f"meta_{self.dataset_name}.json.gz")
        if os.path.exists(meta_file):
            print(f"加载元数据: {meta_file}")
            with gzip.open(meta_file, 'r') as f:
                for line in tqdm(f, desc="处理元数据"):
                    data = json.loads(line)
                    if 'asin' in data:
                        item_meta[data['asin']] = {
                            'title': data.get('title', f"Item_{data['asin']}"),
                            'description': data.get('description', ''),
                            'categories': data.get('categories', [])
                        }
        
        return dict(interactions), item_meta
    
    def _load_local_amazon_data(self) -> Tuple[Dict, Dict]:
        """从本地文件加载Amazon数据"""
        interactions = defaultdict(list)
        item_meta = {}
        
        # 尝试加载已处理的数据
        for split in ['train', 'valid', 'test']:
            file_path = os.path.join(self.data_dir, f"{self.dataset_name}_{split}.txt")
            if os.path.exists(file_path):
                print(f"加载{split}数据: {file_path}")
                with open(file_path, 'r') as f:
                    for line in f:
                        parts = line.strip().split()
                        if len(parts) >= 2:
                            user_id = parts[0]
                            items = parts[1:]
                            for i, item_id in enumerate(items):
                                interactions[user_id].append({
                                    'item_id': item_id,
                                    'timestamp': i,  # 使用序列位置作为时间戳
                                    'rating': 5.0,
                                    'split': split
                                })
        
        # 加载元数据
        meta_file = os.path.join(self.data_dir, 'item_meta.json')
        if os.path.exists(meta_file):
            with open(meta_file, 'r') as f:
                item_meta = json.load(f)
        
        return dict(interactions), item_meta
    
    def _filter_and_sample_data(self, interactions: Dict, sample_ratio: float) -> Dict:
        """过滤和采样数据"""
        print("过滤和采样数据...")
        
        # 统计用户和物品的交互次数
        user_counts = defaultdict(int)
        item_counts = defaultdict(int)
        
        for user_id, user_interactions in interactions.items():
            user_counts[user_id] = len(user_interactions)
            for interaction in user_interactions:
                item_counts[interaction['item_id']] += 1
        
        # 过滤低频用户和物品
        valid_users = {u for u, c in user_counts.items() if c >= self.min_interactions}
        valid_items = {i for i, c in item_counts.items() if c >= self.min_item_interactions}
        
        print(f"过滤前: {len(interactions)}个用户, {len(item_counts)}个物品")
        print(f"过滤后: {len(valid_users)}个用户, {len(valid_items)}个物品")
        
        # 过滤数据
        filtered_interactions = {}
        for user_id in valid_users:
            user_data = []
            for interaction in interactions[user_id]:
                if interaction['item_id'] in valid_items:
                    user_data.append(interaction)
            
            if len(user_data) >= self.min_interactions:
                # 按时间戳排序
                user_data.sort(key=lambda x: x['timestamp'])
                filtered_interactions[user_id] = user_data
        
        # 采样
        if sample_ratio < 1.0:
            sampled_users = random.sample(list(filtered_interactions.keys()), 
                                        int(len(filtered_interactions) * sample_ratio))
            filtered_interactions = {u: filtered_interactions[u] for u in sampled_users}
        
        print(f"最终数据: {len(filtered_interactions)}个用户")
        return filtered_interactions
    
    def _remap_ids(self, interactions: Dict) -> Tuple[Dict, Dict, Dict]:
        """重新映射用户和物品ID"""
        print("重新映射ID...")
        
        # 创建映射
        user_map = {}
        item_map = {}
        user_counter = 0
        item_counter = 0
        
        # 映射用户ID
        for user_id in interactions.keys():
            user_counter += 1
            user_map[user_id] = user_counter
        
        # 映射物品ID
        all_items = set()
        for user_interactions in interactions.values():
            for interaction in user_interactions:
                all_items.add(interaction['item_id'])
        
        for item_id in all_items:
            item_counter += 1
            item_map[item_id] = item_counter
        
        # 重新映射数据
        remapped_interactions = {}
        for user_id, user_interactions in interactions.items():
            new_user_id = user_map[user_id]
            remapped_interactions[new_user_id] = []
            
            for interaction in user_interactions:
                new_item_id = item_map[interaction['item_id']]
                remapped_interactions[new_user_id].append({
                    'item_id': new_item_id,
                    'timestamp': interaction['timestamp'],
                    'rating': interaction['rating'],
                    'split': interaction.get('split', 'train')
                })
        
        print(f"映射完成: {len(user_map)}个用户, {len(item_map)}个物品")
        return remapped_interactions, user_map, item_map
    
    def _split_dataset(self, interactions: Dict) -> Tuple[Dict, Dict, Dict]:
        """分割数据集"""
        print("分割数据集...")
        
        train_data = {}
        valid_data = {}
        test_data = {}
        
        for user_id, user_interactions in interactions.items():
            # 如果数据已经有split标记，使用原有分割
            if 'split' in user_interactions[0]:
                train_items = []
                valid_items = []
                test_items = []
                
                for interaction in user_interactions:
                    if interaction['split'] == 'train':
                        train_items.append(interaction['item_id'])
                    elif interaction['split'] == 'valid':
                        valid_items.append(interaction['item_id'])
                    else:  # test
                        test_items.append(interaction['item_id'])
                
                if train_items:
                    train_data[user_id] = train_items
                if valid_items:
                    valid_data[user_id] = valid_items
                if test_items:
                    test_data[user_id] = test_items
            else:
                # 使用时间序列分割
                items = [interaction['item_id'] for interaction in user_interactions]
                n_items = len(items)
                
                if n_items >= 3:
                    # 最后一个作为测试，倒数第二个作为验证，其余作为训练
                    train_data[user_id] = items[:-2]
                    valid_data[user_id] = [items[-2]]
                    test_data[user_id] = [items[-1]]
                elif n_items == 2:
                    train_data[user_id] = [items[0]]
                    valid_data[user_id] = []
                    test_data[user_id] = [items[1]]
                else:
                    train_data[user_id] = items
                    valid_data[user_id] = []
                    test_data[user_id] = []
        
        print(f"分割完成: 训练{len(train_data)}, 验证{len(valid_data)}, 测试{len(test_data)}")
        return train_data, valid_data, test_data
    
    def _save_processed_data(self, train_data: Dict, valid_data: Dict, test_data: Dict,
                           item_meta: Dict, user_map: Dict, item_map: Dict):
        """保存处理后的数据"""
        print("保存处理后的数据...")
        
        # 保存交互数据 (FedLLM-SRec格式)
        for split, data in [('train', train_data), ('valid', valid_data), ('test', test_data)]:
            output_file = os.path.join(self.output_dir, f"{split}.txt")
            with open(output_file, 'w') as f:
                for user_id, items in data.items():
                    if items:  # 只保存非空的序列
                        f.write(f"{user_id} {' '.join(map(str, items))}\n")
        
        # 保存元数据
        processed_meta = {}
        reverse_item_map = {v: k for k, v in item_map.items()}
        for new_id, old_id in reverse_item_map.items():
            if old_id in item_meta:
                processed_meta[str(new_id)] = item_meta[old_id]
            else:
                processed_meta[str(new_id)] = {
                    'title': f'Item_{new_id}',
                    'description': '',
                    'categories': []
                }
        
        with open(os.path.join(self.output_dir, 'item_meta.json'), 'w') as f:
            json.dump(processed_meta, f, indent=2)
        
        # 保存映射关系
        with open(os.path.join(self.output_dir, 'user_map.json'), 'w') as f:
            json.dump(user_map, f, indent=2)
        
        with open(os.path.join(self.output_dir, 'item_map.json'), 'w') as f:
            json.dump(item_map, f, indent=2)
        
        # 保存统计信息
        stats = {
            'dataset': self.dataset_name,
            'num_users': len(user_map),
            'num_items': len(item_map),
            'num_interactions': {
                'train': sum(len(items) for items in train_data.values()),
                'valid': sum(len(items) for items in valid_data.values()),
                'test': sum(len(items) for items in test_data.values())
            }
        }
        
        with open(os.path.join(self.output_dir, 'stats.json'), 'w') as f:
            json.dump(stats, f, indent=2)
        
        print(f"数据保存完成！统计信息: {stats}")


def main():
    parser = argparse.ArgumentParser(description='统一数据预处理')
    parser.add_argument('--dataset', type=str, required=True,
                       choices=['Movies_and_TV', 'Industrial_and_Scientific', 'Video_Games',
                               'Electronics', 'Books', 'Beauty'],
                       help='数据集名称')
    parser.add_argument('--data_dir', type=str, required=True,
                       help='原始数据目录')
    parser.add_argument('--output_dir', type=str, required=True,
                       help='输出数据目录')
    parser.add_argument('--sample_ratio', type=float, default=1.0,
                       help='采样比例 (0.0-1.0)')
    parser.add_argument('--data_version', type=str, default='2023',
                       choices=['2018', '2023'],
                       help='Amazon数据集版本')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    # 创建处理器
    processor = UnifiedDataProcessor(args.dataset, args.data_dir, args.output_dir)
    
    # 处理数据
    if args.data_version == '2023':
        processor.process_amazon_2023_dataset(args.sample_ratio)
    else:
        processor.process_amazon_2018_dataset(args.sample_ratio)


if __name__ == '__main__':
    main()
